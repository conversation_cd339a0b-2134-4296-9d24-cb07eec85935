import sys
from typing import List, Optional, Literal, Dict, Annotated, Union, TypeVar, Generic, Any, Type, Callable
from pydantic import BaseModel, Field, model_validator
from enum import Enum
import operator

# All the enum classes with NONE option
class AntigenExpressionLevels(str, Enum):
    HIGH = "High Antigen Expression"
    LOW = "Low Antigen Expression"
    MODERATE = "Moderate Antigen Expression"
    NONE = "Not Specified"

class ConcentrationComponents(str, Enum):
    INTACT_ADC = "Intact ADC"
    FREE_PAYLOAD = "Free Payload"
    FREE_ANTIBODY = "Free Antibody"
    OTHER = "Other Components"

class ExperimentType(str, Enum):
    IN_VIVO = "In Vivo Studies"
    IN_VITRO = "In Vitro Studies"
    EX_VIVO = "Ex Vivo Studies"
    NONE = "NONE"
    

class ModelType(str, Enum):
    CELL_LINE = "Cell Line Model"
    CDX = "Cell Line-Derived Xenograft (CDX)"
    PDX = "Patient-Derived Xenograft (PDX)"
    ORGANOID = "Organoid Model"
    SYNGENEIC = "Syngeneic Model"
    TISSUE_SPECIMENS = "Tissue Specimens"
    TRANSGENIC = "Transgenic Model"
    NON_CELL_BASED = "Non-cell based Model"
    RODENT_MODELS = "Rodent Models"
    NON_HUMAN_PRIMATES = "Non-Human Primate Models"
    NONE = "NONE"
   

class LinkerType(str, Enum):
    CLEAVABLE = "Cleavable Linker"
    NON_CLEAVABLE = "Non-cleavable Linker"
    NONE = "NONE"
    

class AntibodyClonality(str, Enum):
    MONOCLONAL = "Monoclonal Antibody (mAb)"
    POLYCLONAL = "Polyclonal Antibody (pAb)"
    NONE = "NONE"     
    

class AntibodySpecies(str, Enum):
    MURINE = "Murine"
    CHIMERIC = "Chimeric"
    HUMANIZED = "Humanized"
    NONE = "NONE"     
    
class AntibodyIsotype(str, Enum):
    IGG = "IgG"
    IGM = "IgM"
    IGA = "IgA"
    IGE = "IgE"
    IGD = "IgD"
    NONE = "NONE"     
   

class EndpointType(str, Enum):
    SAFETY = "safety"
    EFFICACY = "efficacy"
    PHARMACOKINETICS = "pharmacokinetics"
    PHARMACODYNAMICS = "pharmacodynamics"
    BIOMARKER = "biomarker"
    NONE = "NONE"     

class AntibodyDrugConjugateType(str, Enum):
    INVESTIGATIVE = "Investigative"
    REFERENCE = "Reference"
    CONTROL = "Control"  

class EndpointName(BaseModel):
    reasoning: str = Field(..., description="Detailed explanation for why each endpoint was marked as true or false for this specific ADC and experimental model combination on the basis of which you will decide whether the endpoint measurement belongs to this ADC and experimental model or not.")
    
    # Biomarker endpoints
    ANTIGEN_EXPRESSION: bool = Field(..., description="Boolean value indicating antigen expression levels in biological samples categorized qualitatively into High or Low based on predefined thresholds"
    "Only consider clearly mentioned experimental models and not vague mentions.")

    SPECIFIC_ANTIGEN_EXPRESSION_H_SCORE: bool = Field(..., description="Boolean value indicating H-score measurement (0-300) combining staining intensity and percentage of positive cells")
    
    # Efficacy endpoints
    ADC_EC50: bool = Field(..., description="Boolean value indicating presence of concentration at which the ADC or payload achieves 50% of its maximal effect in the specific experiment (binding or cytotoxicity). Strictly consider only the intact ADC and not the antibody or payload components.")
    PAYLOAD_EC50: bool = Field(..., description="Boolean value indicating concentration of free cytotoxic drug required for 50% maximum biological effect. Strictly consider only the free payload and not the intact ADC.")
    ADC_KD: bool = Field(..., description="Boolean value indicating the equilibrium constant quantifying the binding affinity between the antibody component of an ADC and its target antigen, typically expressed in M, mM, µM, nM, or pM")
    ADC_INTERNALIZATION: bool = Field(..., description="Boolean value indicating proportion of ADC successfully internalized by target cells within specified timeframe")
    ADC_TREATMENT_CONCENTRATION: bool = Field(..., description="Boolean value indicating defined amount of ADC applied to co-culture system to evaluate bystander killing effect")
    BM_POS_CELL_DECREASE: bool = Field(..., description="Boolean value indicating reduction in biomarker-positive cells explicitly in coculture system following ADC treatment. Explicitly check for coculture experiments only.")
    BM_NEG_CELL_DECREASE: bool = Field(..., description="A boolean parameter (true/false) that represents whether a measurable reduction in biomarker-negative cells has occurred as a result of the bystander killing effect explicitly in coculture system. Explicitly check for coculture experiments only")    
    ADC_IC50: bool = Field(..., description="Boolean value indicating concentration of intact ADC required to inhibit 50% of cell proliferation or viability. Strictly consider only the intact ADC and not the antibody or payload components. Only consider numerical values for IC50 to be true")
    PAYLOAD_IC50: bool = Field(..., description="Boolean value indicating concentration of free cytotoxic drug required to inhibit 50% of cell proliferation or viability. Strictly consider only the free payload and not the intact ADC.")
    ADC_GI50: bool = Field(..., description="Boolean value indicating concentration required to inhibit 50% of net cell growth compared to controls. Strictly consider only the intact ADC and not the antibody or payload components.")
    ANTI_TUMOR_ACTIVITY_DOSE: bool = Field(..., description="Boolean value indicating the amount of ADC administered to the experimental model for anti-tumor activity assessment."
    "Only consider results where the ADC was administered alone, not in combination with other therapeutic agents.")
    TUMOR_GROWTH_INHIBITION: bool = Field(..., description="Boolean value indicating The measure of the effectiveness of ADC treatment in inhibiting the growth of tumors."
    "TGI is typically expressed as a percentage, indicating the reduction in tumor size or growth rate compared to a control group."
    "Look for quantification methods such as tumor volume measurements (e.g., caliper measurements, bioluminescence imaging, or MRI)." "Only consider results where the ADC was administered alone, not in combination with other therapeutic agents."
     "The T/C ratio (Tumor/Control ratio) is a distinct efficacy metric and must not be extracted or interpreted as Tumor growth inhibition" )    
    OBJECTIVE_RESPONSE_RATE: bool = Field(..., description="Boolean value indicating whether any ADC-treated tumor-bearing animals exhibited a complete or partial response (CR or PR), as defined by categorical response criteria. Excludes general or subjective tumor growth inhibition."
    "Consider only results where the ADC was administered alone, not in combination with other therapeutic agents."
    'TumorGrowthInhibition values should not be captured in OBJECTIVE_RESPONSE_RATE')    
    # PK/PD endpoints
    DAR_TIME_TO_50_PERCENT: bool = Field(..., description="Boolean value indicating time for Drug-to-Antibody Ratio to decrease by 50% in circulation")
    PAYLOAD_RELEASE: bool = Field(..., description="Boolean value indicating percentage of ADC payload released into circulation or tumor site")
    PK_DOSE: bool = Field(..., description="Boolean value indicating administered dose of ADC in preclinical models for ADME evaluation")
    ADC_CMAX: bool = Field(..., description="Boolean value indicating peak PLASMA concentration of intact ADC (antibody-drug conjugate) after administration. MUST be measured in blood/plasma/serum, NOT in tumor tissue. Measures specifically the fully conjugated ADC molecule in systemic circulation where the antibody remains covalently bound to the cytotoxic payload via the linker, typically expressed in µM, µg/mL, or ng/mL. This specifically EXCLUDES any tumor tissue concentrations, deconjugated antibody, unconjugated antibody, or free payload components.")
    ADC_AUC: bool = Field(..., description="Boolean value indicating total systemic exposure of ADC over time. Strictly Consider only the intact ADC and not the antibody or payload components.")
    ADC_T_HALF: bool = Field(..., description="Boolean value indicating whether The time required for the plasma concentration of the intact antibody-drug conjugate (ADC) to decrease by 50% after administration. It specifically reflects the persistence of the fully conjugated ADC, where the antibody remains covalently linked to its payload")
    TAB_CMAX: bool = Field(..., description="Boolean value indicating peak PLASMA concentration of total antibody after administration. MUST be measured in blood/plasma/serum, NOT in tumor tissue. Measures the combined concentration of all antibody species present in systemic circulation: both conjugated antibody (still bound to drug payload) and deconjugated/unconjugated antibody (where drug has been released or cleaved), typically expressed in µM, µg/mL, or ng/mL. This represents total antibody protein regardless of conjugation status and specifically EXCLUDES any tumor tissue concentrations or free payload.")
    TAB_AUC: bool = Field(..., description="Boolean value indicating total exposure of total antibody. Strictly consider only antibody component of ADC and not the full ADC.")
    TAB_T_HALF: bool = Field(..., description="Boolean value indicating time for total antibody to decrease by 50%. Strictly consider only antibody component of ADC and not the full ADC.")
    PAYLOAD_CMAX: bool = Field(..., description="Boolean value indicating peak concentration of free cytotoxic payload after administration. Measures exclusively the unconjugated drug component that has been enzymatically cleaved or chemically released from the antibody-linker complex. Can be measured in plasma/serum OR tumor tissue and expressed in nM, pM, µM, ng/mL, or µg/mL. This specifically excludes any payload molecules still covalently attached to the antibody. Examples include 'released DXd in tumors' or 'free payload in plasma'.")
    PAYLOAD_AUC: bool = Field(..., description="Boolean value indicating total systemic exposure of released cytotoxic payload. Strictly consider only the free payload and not the intact ADC.")
    PAYLOAD_T_HALF: bool = Field(..., description="Boolean value indicating time for plasma concentration of released cytotoxic payload to decrease by 50%. Strictly consider only the free payload and not the intact ADC.")
    ALBUMIN_RECONJUGATION: bool = Field(..., description="Boolean value indicating binding of drug/payload from ADC to serum albumin")
    ANTI_DRUG_ANTIBODIES: bool = Field(..., description="Boolean value indicating antibodies produced by immune system in response to ADC")
    
    # Toxicity endpoints
    TOXICOLOGY_DOSE: bool = Field(..., description="Boolean value indicating drug dose administered per kg in animal toxicity studies")
    TOXICITY_DOSING_REGIMEN: bool = Field(..., description="Boolean value indicating presence of predefined schedule of drug administration to evaluate the safety and toxicological effects of ADC"
    "Do not consider dosing regimens that are not explicitly mentioned in the context of toxicity studies.")
    LETHAL_DOSE: bool = Field(..., description="Boolean value indicating dose causing death in preclinical animal models")
    HNSTD: bool = Field(..., description="Boolean value indicating maximum dose without severe/life-threatening toxic effects")
    DECREASED_FOOD_CONSUMPTION: bool = Field(..., description="Boolean value indicating reduction in the amount of food ingested or utilized by an organism, whether observed directly through measurement of intake or inferred indirectly from associated signs such as altered behavior, physical condition, or physiological indicators. This term encompasses all instances where evidence—whether explicit or implied—suggests lower food intake relative to typical or baseline levels, without equating the phenomenon exclusively with any specific outcome, such as body weight changes. Consider only explicit mentions of decreased food consumption as valid.")
    GI_ISSUES: bool = Field(..., description="Boolean value indicating gastrointestinal toxicity observed")
    DECREASED_BODY_WEIGHT: bool = Field(..., description="Boolean value indicating weight loss due to ADC administration. Consider only explicit mentions of decreased body weight as valid.")
    REDUCED_RED_BLOOD_COUNT: bool = Field(..., description="Boolean value indicating decrease in circulating red blood cells")
    REDUCED_HEMOGLOBIN: bool = Field(..., description="Boolean value indicating lower hemoglobin levels")
    RETICULOCYTE: bool = Field(..., description="Boolean value indicating percentage or count of immature red blood cells")
    REDUCED_ALBUMIN: bool = Field(..., description="Boolean value indicating decrease in serum albumin levels")
    WHITE_BLOOD_CELLS: bool = Field(..., description="Boolean value indicating total count of circulating white blood cells")
    REDUCED_LYMPHOCYTES: bool = Field(..., description="Boolean value indicating decrease in lymphocyte count")
    REDUCED_NEUTROPHILS: bool = Field(..., description="Boolean value Indicating whether neutrophil counts are reduced below normal or clinically relevant thresholds, as supported by explicit citation of measured values or observations within the study context.")
    INCREASED_AST: bool = Field(..., description="Boolean value indicating elevated AST liver enzyme levels")
    INCREASED_ALT: bool = Field(..., description="Boolean value indicating elevated ALT liver enzyme levels")
    #LUNG_INFLAMMATION: bool = Field(..., description="Boolean value indicating inflammatory response in lung tissue")
    PULMONARY_TOXICITY: bool = Field(..., description="Boolean value indicating lung inflammation, Interstitial Lung Disease, fibrosis or other forms of non-infectious lung injury")
    TARGET_ORGAN_ISSUES: bool = Field(..., description="Boolean value indicating organ-specific toxicities caused by ADCs")
    
    # New endpoints
    HETEROGENEITY_INDEX: bool = Field(..., description="Boolean value indicating measure of variability in drug-to-antibody ratio (DAR) among molecules in an ADC product")
    ANTIGEN_SHEDDING: bool = Field(..., description="Boolean value indicating the process by which target antigen is cleaved and released from cancer cell surface into bloodstream")

class Endpoint(BaseModel):
    """Base class for all endpoints"""
    measured_value: str = Field(None, description="The value of the endpoint")
    measured_time: str = Field(None, description="The timepoint at which the endpoint is measured")
    measured_concentration: str = Field(None, description="The concentration of the ADC at the timepoint of measurement")

# Refactored Pydantic Models

# ADC
class AntibodyDrugConjugate(BaseModel):
    """Information about ADC: Antibody Drug Conjugate with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    adc_name: str = Field(..., description="The name of the Antibody Drug Conjugate")
    adc_company: str = Field(..., description="The institutional or organizational association of an author at the time the research was conducted or the manuscript was written")

    adc_type: AntibodyDrugConjugateType = Field(..., description='''The type of the above ADC, whether it is an investigative or reference ADC. 'Investigative ADC' means any antibody–drug conjugate that is directly tested or evaluated in the preclinical experimental sections (in vitro laboratory experiments or in vivo animal models) of the manuscript.
    'Reference ADC' means any antibody–drug conjugate that is only mentioned as a comparator, example, or benchmark without being experimentally tested in any preclinical experiments within the study.
     'Control ADC' is a specially designed ADC used as a negative or non-targeting control in preclinical or laboratory studies. Its main purpose is to help distinguish specific effects of a test ADC (targeted against a tumor-associated antigen) from non-specific effects that might occur due to the ADC scaffold, linker or cytotoxic payload.''')
    #Antibody fields
    antibody_name: str = Field(..., description="The name of the antibody used in the above ADC")
    antibody_binding_epitope: str = Field(..., description="The specific region on an antigen that is recognized and bound by an antibody. It is the molecular address where the antibody docks to initiate an immune response or therapeutic effect.It is usually reprsented as short peptise sequence of amino acids")
    antibody_binding_epitope_location: str = Field(..., description="A categorical descriptor indicating whether the antibody’s binding site (epitope) on the extracellular domain (ECD) of a membrane-bound target protein is located near or far from the cell memebrane. Membrane Proximal epitopes are situated close to the cell membrane, typically within membrane-adjacent subdomains (e.g., subdomain IV of HER2).Membrane Distal epitopes are located farther from the membrane, usually in the outermost subdomains of the ECD (e.g., subdomain I of HER2).")
    adc_species_cross_reactivity: str = Field(..., description="the ability of the ADC to bind to the same or similar antigen in different animal species (e.g., mouse, rat, monkey, dog) as it does in humans.")
    antibody_clonality: AntibodyClonality = Field(..., description="The type of clonality for the above antibody")
    antibody_species: AntibodySpecies = Field(..., description="The species origin of the above antibody")
    antibody_isotype: AntibodyIsotype = Field(..., description="The isotype classification of the above antibody")
    #Payload fields
    payload_name: str = Field(..., description="The name of the chemical compound used as payload to be delivered using above ADC")    
    payload_target: str = Field(..., description="The specific molecular target or pathway that the above payload is designed to affect")
    #Linker fields
    linker_name: str = Field(..., description="The name of the chemical compound used as a linker to bind antibody and payload together in the above ADC")
    linker_type: LinkerType = Field(..., description="The cleavable property of the above linker")
    #Antigen fields
    antigen_name: str = Field(..., description="The name of the antigen targeted by the above ADC")
    
    # Additional fields
    ss_conjugation: bool = Field(None, description="Indicates whether the ADC is conjugated at a specific site on the antibody")
    ss_conjugation_technology: str = Field(None, description="Technology used for site-specific conjugation (e.g., engineered amino acids, enzymatic methods)")
    conjugation_amino_acid: str = Field(None, description="Amino acid on the antibody used for drug or payload attachment (Cysteine, Lysine, Glutamine, Glycan, etc.)")
    conjugation_sites: List[str] = Field(default_factory=list, description="Specific locations on the antibody where the drug is conjugated")
    drug_to_antibody_ratio: str = Field(None, description="Average number of payload molecules conjugated per antibody")
    clinical_trial_phase: str = Field(None, description="Current clinical trial phase of the ADC")
    clinical_data_availability: bool = Field(None, description="Indicates whether clinical trial data is available")

# TODO - Edit adc agent prompt - to call load_adc_names_into_memory Tool to extract and save the ADC names in memory

class ExperimentalModel(BaseModel):
    """Information about experimental model used in the preclinical experiment for given ADC with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    model_name: str = Field(..., description="The exact, detailed designation of the experimental biological system as stated in the original source or publication, including all specific identifiers such as species, strain, cell type, disease state, genetic modification, engraftment status, and any other relevant distinguishing features that uniquely identify the model without abstraction or generalization.")
    clinical_human_related: bool = Field(..., description="Indicates whether clinical data is available for this model.Clinical data refers to any information collected about a human subject in the context of healthcare delivery, clinical trials, or medical research. It encompasses a wide range of data types that describe a patient's health status, medical history, diagnostic results, treatments, and outcomes."
    )
    cancer_type: str = Field(..., description="A cancer type refers to the broad, primary classification of cancer, based on the organ or tissue where the cancer originates. It is essentially the location in the body where the cancerous cells began to form.")
    cancer_subtype: Optional[str] = Field(None, description="A cancer subtype is a further classification within a cancer type, based on specific histological, genetic, or molecular characteristics of the tumor cells. Subtypes can influence the cancer's behavior, its response to treatment, and its prognosis.")
    investigative: bool = Field(..., description="Indicates whether the model has been used to investigate the specified antibody drug conjugate. This is true if the model has ANY endpoint measurement reported for the given ADC in the preclinical experiment. If the model has not been used to investigate the specified ADC, this should be false.")
    reasoning_for_inclusion: str = Field(..., description="The reasoning and criteria that led to this model being selected for extraction. This should explain why this particular experimental model is relevant to the ADC study, what makes it suitable for the research objectives, and what specific characteristics or capabilities make it valuable for investigating the ADC's properties or effects.")



class Experiment(BaseModel):
    """Information about the experiment conducted on the experimental model for given ADC """
    reasoning_for_inclusion: str = Field(..., description="The reasoning and criteria that led to this experiment being selected for extraction. This should explain why this particular experiment is relevant to the ADC study, what makes it suitable for the research objectives, and what specific characteristics or capabilities make it valuable for investigating the ADC's properties or effects."
    "Use this reasoning to justify why the extracted measured value, measured dose, measured time, etc. is correct for this experiment.")
    model_name: str = Field(..., description="The exact, detailed designation of the experimental biological system as stated in the original source or publication, including all specific identifiers such as species, strain, cell type, disease state, genetic modification, engraftment status, and any other relevant distinguishing features that uniquely identify the model without abstraction or generalization."
    "When describing experimental models, include any critical treatments, additives, or interventions (such as specific enzymes, chemicals, or conditions) that are mentioned in the context.")

    experiment_type: ExperimentType = Field(..., description="The type of experiment conducted on this model for given ADC")
    model_type: ModelType = Field(..., description="A standardized, categorical classification selected from a controlled vocabulary, representing the general nature of the biological system described in experimental context of the endpoint measurement assigned strictly according to formal ontological rules and domain standards.")

    @model_validator(mode='after')
    def validate_experiment_model_consistency(self):
        """
        Ensures logical consistency between experiment_type and model_type.
        The allowed combinations below are based on domain relationships; for example,
        in vivo experiments must use in vivo model types, etc.
        """
        experiment_type = self.experiment_type
        model_type = self.model_type

        # Define valid combinations using enum values
        valid_combinations = {
            ExperimentType.IN_VITRO: {ModelType.CELL_LINE, ModelType.NON_CELL_BASED, ModelType.ORGANOID, ModelType.NONE},
            ExperimentType.EX_VIVO: {ModelType.TISSUE_SPECIMENS, ModelType.NONE},
            ExperimentType.IN_VIVO: {
                ModelType.CDX, ModelType.PDX, ModelType.SYNGENEIC,
                ModelType.TRANSGENIC, ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE
            },
            ExperimentType.NONE: {
                ModelType.CELL_LINE, ModelType.NON_CELL_BASED, ModelType.ORGANOID, ModelType.TISSUE_SPECIMENS,
                ModelType.CDX, ModelType.PDX, ModelType.SYNGENEIC, ModelType.TRANSGENIC,
                ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE
            }
        }

        if model_type not in valid_combinations[experiment_type]:
            valid_options = [option.value for option in valid_combinations[experiment_type]]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination: {experiment_type.value} experiments cannot use '{model_type.value}' models. Recheck your answer!\nThe valid combinations are: {', '.join(valid_options)}."
            )

        return self

# Specialized endpoint models named exactly after the EndpointName enum values
class AntigenExpression(Experiment):
    """Endpoint Information about Antigen Expression for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: AntigenExpressionLevels = Field(None, description="Categorization of the level of antigen expression in biological samples such as tumor cell lines, primary cells, or tissue specimens, categorized as high, low, or moderate based on predefined thresholds")

    @model_validator(mode='after')
    def validate_model_type_for_antigen_expression(self):
        """Validate that model_type is compatible with AntigenExpression endpoint."""
        allowed_model_types = {ModelType.CELL_LINE, ModelType.ORGANOID, ModelType.TISSUE_SPECIMENS, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for AntigenExpression: {self.model_type.value}. Antigen Expression cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for AntigenExpression are: {', '.join(allowed_values)}. and map the model name with the right model type and experiment type understanding the endpoint context. Do not exclude the entry for this model until and unless you are not able to map it to the right model type and experiment type."
            )
        return self

class SpecificAntigenExpressionHScore(Experiment):
    """Endpoint measurement of antigen expression using H-score for ADC target validation with supporting citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="H-score value (0-300) representing semi-quantitative immunohistochemistry assessment of antigen expression intensity and distribution in tissue specimens or immunocytochemistry in cell lines")

    @model_validator(mode='after')
    def validate_model_type_for_specific_antigen_expression_h_score(self):
        """Validate that model_type is compatible with SpecificAntigenExpressionHScore endpoint."""
        allowed_model_types = {ModelType.CELL_LINE, ModelType.ORGANOID, ModelType.TISSUE_SPECIMENS, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for SpecificAntigenExpressionHScore: {self.model_type.value}. H Score cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for SpecificAntigenExpressionHScore are: {', '.join(allowed_values)}."
            )
        return self

class AdcEc50(Experiment):
    """Endpoint detailing a specific EC50 measurement for an ADC tested on a defined experimental model, including source citations and assay metadata."""
    citations: List[str] = Field(..., description="Sentences or references from the research article where the information supporting this EC50 measurement was extracted or interpreted.")
    measured_value: str = Field(None, description="Numeric value of EC50, representing the concentration of intact ADC (not string), required to reach 50% of the maximum biological effect, typically expressed in nM, pM, µM, ng/mL, or µg/mL")
    measured_time: str = Field(None, description="Time after ADC administration or experiment initiation when EC50 was assessed (e.g., '72 hours post-treatment')")

    @model_validator(mode='after')
    def validate_model_type_for_adc_ec50(self):
        """Validate that model_type is compatible with AdcEc50 endpoint."""
        allowed_model_types = {ModelType.CELL_LINE, ModelType.NON_CELL_BASED, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for AdcEc50: {self.model_type.value}. ADC EC50 cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for AdcEc50 are: {', '.join(allowed_values)}."
            )
        return self

class PayloadEc50(Experiment):
    """Endpoint Information about a unique measurement of Payload EC50 for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The concentration of the free (unconjugated) cytotoxic drug required to achieve 50% of its maximum biological effect, typically expressed in nM, pM, µM, ng/mL, or µg/mL")
    measured_time: str = Field(None, description="The specific time period after payload administration that the EC50 value is measured")

    @model_validator(mode='after')
    def validate_model_type_for_payload_ec50(self):
        """Validate that model_type is compatible with PayloadEc50 endpoint."""
        allowed_model_types = {ModelType.CELL_LINE, ModelType.NON_CELL_BASED, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for PayloadEc50: {self.model_type.value}. Payload EC50 cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for PayloadEc50 are: {', '.join(allowed_values)}."
            )
        return self

class AdcKd(Experiment):
    """Endpoint Information about ADC Kd (dissociation constant) for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The dissociation constant (Kd) quantifying the binding affinity between the antibody component of an ADC and its target antigen, typically expressed in M, mM, µM, nM, or pM")
    
    # @model_validator(mode='after')
    # def validate_model_type_for_adc_kd(self):
    #     """Validate that model_type is compatible with AdcKd endpoint."""
    #     allowed_model_types = {ModelType.CELL_LINE, ModelType.NON_CELL_BASED, ModelType.NONE}
    #     if self.model_type not in allowed_model_types:
    #         allowed_values = [mt.value for mt in allowed_model_types]
    #         raise ValueError(
    #             f"For the model {self.model_name}, the measurement provided has an invalid combination for AdcKd: {self.model_type.value}. ADC Kd cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for AdcKd are: {', '.join(allowed_values)}."
    #         )
    #     return self

class AdcInternalization(Experiment):
    """Endpoint Information about ADC internalization for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The proportion of the total administered ADC that is successfully internalized by target cells, typically expressed as a percentage")
    measured_time: str = Field(None, description="The time post-ADC exposure at which internalization of the ADC is quantitatively or qualitatively assessed, typically expressed as minutes (min) or hours (h)")
    measured_concentration: str = Field(None, description="The concentration of the antibody-drug conjugate (ADC) applied to cells or assay systems during internalization studies. This is typically expressed in units such as nanomolar (nM), picomolar (pM), micrograms per milliliter (µg/mL), or nanograms per milliliter (ng/mL), depending on the experimental setup")
    
    @model_validator(mode='after')
    def validate_model_type_for_adc_internalization(self):
        """Validate that model_type is compatible with AdcInternalization endpoint."""
        allowed_model_types = {ModelType.CELL_LINE, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for AdcInternalization: {self.model_type.value}. ADC Internalization cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for AdcInternalization are: {', '.join(allowed_values)}."
            )
        return self

class AdcTreatmentConcentration(Experiment):
    """Endpoint Information about ADC treatment concentration for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The defined amount of ADC applied to a co-culture system of biomarker-positive and biomarker-negative cells, typically expressed in molar units such as nM or pM")
    
    @model_validator(mode='after')
    def validate_model_type_for_adc_treatment_concentration(self):
        """Validate that model_type is compatible with AdcTreatmentConcentration endpoint."""
        allowed_model_types = {ModelType.CELL_LINE, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for AdcTreatmentConcentration: {self.model_type.value}. ADC Treatment Concentration cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for AdcTreatmentConcentration are: {', '.join(allowed_values)}."
            )
        return self

class BmPosCellDecrease(Experiment):
    """Endpoint Information about biomarker-positive cell decrease for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The reduction in the number or viability of cells that express the target antigen following treatment with an ADC, typically expressed as percentage decrease relative to untreated control")
    measured_time: str = Field(None, description="The time point at which the cell decrease is measured after ADC treatment")
    
    @model_validator(mode='after')
    def validate_model_type_for_bm_pos_cell_decrease(self):
        """Validate that model_type is compatible with BmPosCellDecrease endpoint."""
        allowed_model_types = {ModelType.CELL_LINE, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for BmPosCellDecrease: {self.model_type.value}. Biomarker-positive cell decrease cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for BmPosCellDecrease are: {', '.join(allowed_values)}."
            )
        return self

class BmNegCellDecrease(Experiment):
    """Endpoint Information about biomarker-negative cell decrease for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="A quantitative or qualitative assessment representing the reduction in the number or viability of cells not expressing the target antigen after tbreatment with an antibody-drug conjugate (ADC), typically expressed as a percentage decrease relative to an untreated control")
    measured_time: str = Field(None, description="The time point at which the cell decrease is measured after ADC treatment")
    
    @model_validator(mode='after')
    def validate_model_type_for_bm_neg_cell_decrease(self):
        """Validate that model_type is compatible with BmNegCellDecrease endpoint."""
        allowed_model_types = {ModelType.CELL_LINE, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for BmNegCellDecrease: {self.model_type.value}. Biomarker-negative cell decrease cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for BmNegCellDecrease are: {', '.join(allowed_values)}."
            )
        return self

class AdcIc50(Experiment):
    """Endpoint Information about ADC IC50 for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The concentration of the intact antibody-drug conjugate required to inhibit 50% of cell proliferation or viability, typically expressed in nM, pM, µM, ng/mL, or µg/mL. Only capture the value without any additional information.")
    measured_time: str = Field(None, description="The time point at which the IC50 is measured after ADC treatment")
    
    @model_validator(mode='after')
    def validate_model_type_for_adc_ic50(self):
        """Validate that model_type is compatible with AdcIc50 endpoint."""
        allowed_model_types = {ModelType.CELL_LINE, ModelType.NON_CELL_BASED, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for AdcIc50: {self.model_type.value}. ADC IC50 cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for AdcIc50 are: {', '.join(allowed_values)}."
            )
        return self

class PayloadIc50(Experiment):
    """Endpoint Information about Payload IC50 for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The concentration of the free (unconjugated) cytotoxic drug required to inhibit 50% of cell proliferation or viability, typically expressed in nM, pM, µM, ng/mL, or µg/mL")
    measured_time: str = Field(None, description="The time point at which the IC50 is measured after payload treatment")
    
    @model_validator(mode='after')
    def validate_model_type_for_payload_ic50(self):
        """Validate that model_type is compatible with PayloadIc50 endpoint."""
        allowed_model_types = {ModelType.CELL_LINE, ModelType.NON_CELL_BASED, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for PayloadIc50: {self.model_type.value}. Payload IC50 cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for PayloadIc50 are: {', '.join(allowed_values)}."
            )
        return self

class AdcGi50(Experiment):
    """Endpoint Information about ADC GI50 for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The concentration of an ADC required to inhibit 50% of net cell growth compared to untreated controls, typically expressed in nM, pM, µM, ng/mL, or µg/mL")
    measured_time: str = Field(None, description="The time period over which the growth inhibition is measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_adc_gi50(self):
        """Validate that model_type is compatible with AdcGi50 endpoint."""
        allowed_model_types = {ModelType.CELL_LINE, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for AdcGi50: {self.model_type.value}. ADC GI50 cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for AdcGi50 are: {', '.join(allowed_values)}."
            )
        return self

class AntiTumorActivityDose(Experiment):
    """Endpoint Information about anti-tumor activity dose for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="Single measurement of dose that is reported in anti tumor activity. Extract only the dose value without any additional information.")
    
    @model_validator(mode='after')
    def validate_model_type_for_anti_tumor_activity_dose(self):
        """Validate that model_type is compatible with AntiTumorActivityDose endpoint."""
        allowed_model_types = {ModelType.CDX, ModelType.PDX, ModelType.SYNGENEIC, ModelType.TRANSGENIC, ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for AntiTumorActivityDose: {self.model_type.value}. Anti-tumor activity dose cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for AntiTumorActivityDose are: {', '.join(allowed_values)}."
            )
        return self

class TumorGrowthInhibition(Experiment):
    """Endpoint Information about tumor growth inhibition for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The measure of the effectiveness of a treatment in inhibiting the growth of tumors. TGI is typically expressed as a percentage, indicating the reduction in tumor size or growth rate compared to a control group. It is usually quantified using methods such as Caliper measurement or Imaging techniques. It should strictly be reported as a percentage value if reported as a percentage in the paper.")
    measured_time: str = Field(None, description="The specific timepoint post-ADC administration at which tumor growth inhibition (TGI) is quantitatively assessed. This is typically expressed in days (d) and corresponds to the day on which tumor volumes in treated versus control groups are compared to calculate the percentage of growth inhibition. The selected timepoint reflects the duration of the in vivo study and is often aligned with the last day of treatment or a predefined study endpoint")
    measured_dose: str = Field(None, description="Specific dose of the therapeutic agent, in this case antibody drug conjugate ( ADC) administered in the study, typically expressed as mg/kg")
    
    @model_validator(mode='after')
    def validate_model_type_for_tumor_growth_inhibition(self):
        """Validate that model_type is compatible with TumorGrowthInhibition endpoint."""
        allowed_model_types = {ModelType.CDX, ModelType.PDX, ModelType.SYNGENEIC, ModelType.TRANSGENIC, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for TumorGrowthInhibition: {self.model_type.value}. Tumor growth inhibition cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for TumorGrowthInhibition are: {', '.join(allowed_values)}."
            )
        return self

class ObjectiveResponseRate(Experiment):
    """Endpoint Information about objective response rate for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The proportion of subjects showing a predefined amount of tumor size reduction, usually defined as achieving complete or partial response, according to standardized criteria such as RECIST, within a specified assessment period.")
    measured_dose: str = Field(None, description="The dose of ADC at which the objective response rate is measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_objective_response_rate(self):
        """Validate that model_type is compatible with ObjectiveResponseRate endpoint."""
        allowed_model_types = {ModelType.CDX, ModelType.PDX, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for ObjectiveResponseRate: {self.model_type.value}. Objective response rate cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for ObjectiveResponseRate are: {', '.join(allowed_values)}."
            )
        return self

class DarTimeTo50Percent(Experiment):
    """Endpoint Information about DAR time to 50% for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The time required for the Drug-to-Antibody Ratio (DAR) to decrease by 50% in circulation, typically expressed in hours or days")
    
    @model_validator(mode='after')
    def validate_model_type_for_dar_time_to_50_percent(self):
        """Validate that model_type is compatible with DarTimeTo50Percent endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for DarTimeTo50Percent: {self.model_type.value}. DAR time to 50% cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for DarTimeTo50Percent are: {', '.join(allowed_values)}."
            )
        return self

class PayloadRelease(Experiment):
    """Endpoint Information about payload release for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The percentage of ADC payload released into circulation or tumor site")
    measured_time: str = Field(None, description="The time point at which payload release is measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_payload_release(self):
        """Validate that model_type is compatible with PayloadRelease endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.CDX, ModelType.PDX, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for PayloadRelease: {self.model_type.value}. Payload release cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for PayloadRelease are: {', '.join(allowed_values)}."
            )
        return self

class PkDose(Experiment):
    """Endpoint Information about PK dose for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The administered dose of an ADC in preclinical models to evaluate its absorption, distribution, metabolism, and excretion (ADME)")
    
    @model_validator(mode='after')
    def validate_model_type_for_pk_dose(self):
        """Validate that model_type is compatible with PkDose endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for PkDose: {self.model_type.value}. PK dose cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for PkDose are: {', '.join(allowed_values)}."
            )
        return self

class AdcCmax(Experiment):
    """Endpoint Information about ADC Cmax for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The peak PLASMA concentration of the intact antibody-drug conjugate (ADC) where the antibody remains covalently bound to the cytotoxic payload, typically expressed as µM, µg/mL, or ng/mL. This MUST be measured in blood/plasma/serum from pharmacokinetic studies, NEVER in tumor tissue. Specifically excludes any tumor concentrations, deconjugated antibody, and free payload. Look for terms like 'plasma T-DXd Cmax' or 'serum ADC concentration', NOT 'tumor DXd' or 'released payload'.")
    measured_dose: str = Field(None, description="The dose at which the Cmax is measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_adc_cmax(self):
        """Validate that model_type is compatible with AdcCmax endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for AdcCmax: {self.model_type.value}. ADC Cmax cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for AdcCmax are: {', '.join(allowed_values)}."
            )
        return self

class AdcAuc(Experiment):
    """Endpoint Information about ADC AUC for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The total systemic exposure of the ADC over time, typically expressed as µg*h/mL, mg*h/L, or ng*h/mL")
    measured_dose: str = Field(None, description="The dose at which the AUC is measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_adc_auc(self):
        """Validate that model_type is compatible with AdcAuc endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for AdcAuc: {self.model_type.value}. ADC AUC cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for AdcAuc are: {', '.join(allowed_values)}."
            )
        return self

class HeterogeneityIndex(Experiment):
    """Endpoint Information about Heterogeneity Index for the given ADC with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="A numerical value representing the variability in drug-to-antibody ratio (DAR) among molecules in an ADC product")
    
    @model_validator(mode='after')
    def validate_model_type_for_heterogeneity_index(self):
        """Validate that model_type is compatible with HeterogeneityIndex endpoint."""
        allowed_model_types = {ModelType.NON_CELL_BASED, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for HeterogeneityIndex: {self.model_type.value}. Heterogeneity Index cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for HeterogeneityIndex are: {', '.join(allowed_values)}."
            )
        return self

class AntigenShedding(Experiment):
    """Endpoint Information about Antigen Shedding for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The rate or amount of target antigen cleaved and released from cancer cell surface into bloodstream, typically expressed as a concentration or percentage")
    measured_time: str = Field(None, description="The time point at which antigen shedding is measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_antigen_shedding(self):
        """Validate that model_type is compatible with AntigenShedding endpoint."""
        allowed_model_types = {ModelType.CELL_LINE, ModelType.CDX, ModelType.PDX, ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for AntigenShedding: {self.model_type.value}. Antigen Shedding cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for AntigenShedding are: {', '.join(allowed_values)}."
            )
        return self

class AdcTHalf(Experiment):
    """Endpoint Information about ADC half-life for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The time required for the plasma concentration of the full ADC to decrease by 50%, typically expressed in hours or days")
    measured_dose: str = Field(None, description="The dose at which the half-life is measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_adc_t_half(self):
        """Validate that model_type is compatible with AdcTHalf endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for AdcTHalf: {self.model_type.value}. ADC half-life cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for AdcTHalf are: {', '.join(allowed_values)}."
            )
        return self

class TabCmax(Experiment):
    """Endpoint Information about total antibody Cmax for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The peak PLASMA concentration of total antibody protein, representing the combined concentration of both conjugated antibody (still bound to payload) and deconjugated/unconjugated antibody (payload released), typically expressed as µM, µg/mL, or ng/mL. This MUST be measured in blood/plasma/serum from pharmacokinetic studies, NEVER in tumor tissue. Includes all antibody species regardless of conjugation status. Look for terms like 'plasma total antibody Cmax' or 'serum antibody concentration', NOT 'tumor antibody' or 'tissue concentrations'.")
    measured_dose: str = Field(None, description="The dose at which the Cmax is measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_tab_cmax(self):
        """Validate that model_type is compatible with TabCmax endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for TabCmax: {self.model_type.value}. Total antibody Cmax cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for TabCmax are: {', '.join(allowed_values)}."
            )
        return self

class TabAuc(Experiment):
    """Endpoint Information about total antibody AUC for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The total exposure of total antibody, typically expressed as µg*h/mL, mg*h/L, or ng*h/mL")
    measured_dose: str = Field(None, description="The dose at which the AUC is measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_tab_auc(self):
        """Validate that model_type is compatible with TabAuc endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for TabAuc: {self.model_type.value}. Total antibody AUC cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for TabAuc are: {', '.join(allowed_values)}."
            )
        return self

class TabTHalf(Experiment):
    """Endpoint Information about total antibody half-life for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The time required for the total antibody to decrease by 50%, typically expressed in hours or days")
    measured_dose: str = Field(None, description="The dose at which the half-life is measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_tab_t_half(self):
        """Validate that model_type is compatible with TabTHalf endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for TabTHalf: {self.model_type.value}. Total antibody half-life cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for TabTHalf are: {', '.join(allowed_values)}."
            )
        return self

class PayloadCmax(Experiment):
    """Endpoint Information about payload Cmax for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The peak concentration of the free cytotoxic payload that has been enzymatically cleaved or chemically released from the antibody-linker complex, typically expressed as nM, pM, µM, ng/mL, or µg/mL. This can be measured in plasma/serum OR tumor tissue. Specifically excludes any payload molecules still covalently attached to the antibody and any antibody components. Look for terms like 'released DXd in tumors', 'free payload in plasma', 'tumor DXd concentration', or 'unconjugated drug levels'.")
    measured_dose: str = Field(None, description="The dose at which the Cmax is measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_payload_cmax(self):
        """Validate that model_type is compatible with PayloadCmax endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for PayloadCmax: {self.model_type.value}. Payload Cmax cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for PayloadCmax are: {', '.join(allowed_values)}."
            )
        return self

class PayloadAuc(Experiment):
    """Endpoint Information about payload AUC for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The total systemic exposure of released cytotoxic payload, typically expressed as ng*h/mL")
    measured_dose: str = Field(None, description="The dose at which the AUC is measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_payload_auc(self):
        """Validate that model_type is compatible with PayloadAuc endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for PayloadAuc: {self.model_type.value}. Payload AUC cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for PayloadAuc are: {', '.join(allowed_values)}."
            )
        return self

class PayloadTHalf(Experiment):
    """Endpoint Information about payload half-life for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The time required for the plasma concentration of released cytotoxic payload to decrease by 50%, typically expressed in hours or days")
    measured_dose: str = Field(None, description="The dose at which the half-life is measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_payload_t_half(self):
        """Validate that model_type is compatible with PayloadTHalf endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for PayloadTHalf: {self.model_type.value}. Payload half-life cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for PayloadTHalf are: {', '.join(allowed_values)}."
            )
        return self

class AlbuminReconjugation(Experiment):
    """Endpoint Information about albumin reconjugation for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The extent of binding of drug/payload from ADC to serum albumin, typically expressed as a percentage or concentration")
    measured_time: str = Field(None, description="The time point at which albumin reconjugation is measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_albumin_reconjugation(self):
        """Validate that model_type is compatible with AlbuminReconjugation endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for AlbuminReconjugation: {self.model_type.value}. Albumin reconjugation cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for AlbuminReconjugation are: {', '.join(allowed_values)}."
            )
        return self

class AntiDrugAntibodies(Experiment):
    """Endpoint Information about anti-drug antibodies for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The presence or level of antibodies produced by the immune system in response to ADC")
    measured_time: str = Field(None, description="The time point at which anti-drug antibodies are measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_anti_drug_antibodies(self):
        """Validate that model_type is compatible with AntiDrugAntibodies endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for AntiDrugAntibodies: {self.model_type.value}. Anti-drug antibodies cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for AntiDrugAntibodies are: {', '.join(allowed_values)}."
            )
        return self

class ToxicologyDose(Experiment):
    """Endpoint Information about toxicology dose for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The drug dose administered per kg in animal toxicity studies")
    
    @model_validator(mode='after')
    def validate_model_type_for_toxicology_dose(self):
        """Validate that model_type is compatible with ToxicologyDose endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for ToxicologyDose: {self.model_type.value}. Toxicology dose cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for ToxicologyDose are: {', '.join(allowed_values)}."
            )
        return self

class ToxicityDosingRegimen(Experiment):
    """Endpoint Information about toxicity dosing regimen for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="How often the drug is administered (e.g., once weekly, twice weekly)")
    measured_dose: str = Field(None, description="Specific dose of the therapeutic agent, in this case antibody drug conjugate ( ADC) administered in the study, typically expressed as mg/kg"
    "Only extract the dosing regimen if it is explicitly mentioned in the context of toxicity studies."
    "Do not extract hnstd, lethal dose, etc. as the dosing regimen.")

    @model_validator(mode='after')
    def validate_model_type_for_toxicity_dosing_regimen(self):
        """Validate that model_type is compatible with ToxicityDosingRegimen endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for ToxicityDosingRegimen: {self.model_type.value}. ToxicityDosingRegimen cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for TToxicityDosingRegimen are: {', '.join(allowed_values)}."
            )
        return self

class LethalDose(Experiment):

    """Endpoint Information about lethal dose for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The dose causing death in preclinical animal models, typically expressed in mg/kg. Only include the dose at which death is observed and not the maximum dose administered / tested to be safe for this experimental model.")

    
    @model_validator(mode='after')
    def validate_model_type_for_lethal_dose(self):
        """Validate that model_type is compatible with LethalDose endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for LethalDose: {self.model_type.value}. Lethal dose cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for LethalDose are: {', '.join(allowed_values)}."
            )
        return self

class Hnstd(Experiment):
    """Endpoint Information about highest non-severely toxic dose for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The maximum dose without severe/life-threatening toxic effects")
    
    @model_validator(mode='after')
    def validate_model_type_for_hnstd(self):
        """Validate that model_type is compatible with Hnstd endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for Hnstd: {self.model_type.value}. HNSTD cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for Hnstd are: {', '.join(allowed_values)}."
            )
        return self

class DecreasedFoodConsumption(Experiment):
    """Endpoint Information about decreased food consumption for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The reduction in food intake due to ADC, typically expressed as a percentage or amount")
    measured_dose: str = Field(None, description="The dose at which decreased food consumption is observed")
    
    @model_validator(mode='after')
    def validate_model_type_for_decreased_food_consumption(self):
        """Validate that model_type is compatible with DecreasedFoodConsumption endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for DecreasedFoodConsumption: {self.model_type.value}. Decreased food consumption cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for DecreasedFoodConsumption are: {', '.join(allowed_values)}."
            )
        return self

class GiIssues(Experiment):
    """Endpoint Information about GI issues for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The gastrointestinal toxicity observed, typically described qualitatively or quantitatively")
    measured_dose: str = Field(None, description="The dose at which GI issues are observed")
    
    @model_validator(mode='after')
    def validate_model_type_for_gi_issues(self) :
        """Validate that model_type is compatible with GiIssues endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for GiIssues: {self.model_type.value}. GI issues cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for GiIssues are: {', '.join(allowed_values)}."
            )
        return self

class DecreasedBodyWeight(Experiment):
    """Endpoint Information about decreased body weight for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="Sentences from research papers supporting the extracted information about decreased body weight following ADC treatment for that model.")
    measured_value: str = Field(None, description="Observed reduction in body weight after ADC administration, typically as a percentage (%) or absolute value (e.g., grams).")
    measured_dose: str = Field(None, description="Specific ADC dose (e.g., mg/kg) at which decreased body weight was observed for that experimental model.")
    
    @model_validator(mode='after')
    def validate_model_type_for_decreased_body_weight(self):
        """Validate that model_type is compatible with DecreasedBodyWeight endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for DecreasedBodyWeight: {self.model_type.value}. Decreased body weight cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for DecreasedBodyWeight are: {', '.join(allowed_values)}."
            )
        return self

class ReducedRedBloodCount(Experiment):
    """Endpoint Information about reduced red blood count for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The decrease in circulating red blood cells, typically expressed as a count or percentage")
    measured_dose: str = Field(None, description="The dose at which reduced red blood count is observed")
    
    @model_validator(mode='after')
    def validate_model_type_for_reduced_red_blood_count(self):
        """Validate that model_type is compatible with ReducedRedBloodCount endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for ReducedRedBloodCount: {self.model_type.value}. Reduced red blood count cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for ReducedRedBloodCount are: {', '.join(allowed_values)}."
            )
        return self

class ReducedHemoglobin(Experiment):
    """Endpoint Information about reduced hemoglobin for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The lower hemoglobin levels, typically expressed in g/dL")
    measured_dose: str = Field(None, description="The dose at which reduced hemoglobin is observed")
    
    @model_validator(mode='after')
    def validate_model_type_for_reduced_hemoglobin(self):
        """Validate that model_type is compatible with ReducedHemoglobin endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for ReducedHemoglobin: {self.model_type.value}. Reduced hemoglobin cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for ReducedHemoglobin are: {', '.join(allowed_values)}."
            )
        return self

class Reticulocyte(Experiment):
    """Endpoint Information about reticulocyte for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The percentage or absolute count of immature red blood cells in circulation")
    measured_dose: str = Field(None, description="The dose at which reticulocyte count is measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_reticulocyte(self):
        """Validate that model_type is compatible with Reticulocyte endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for Reticulocyte: {self.model_type.value}. Reticulocyte count cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for Reticulocyte are: {', '.join(allowed_values)}."
            )
        return self

class ReducedAlbumin(Experiment):
    """Endpoint Information about reduced albumin for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The decrease in serum albumin levels, typically expressed in g/dL")
    measured_dose: str = Field(None, description="The dose at which reduced albumin is observed")
    
    @model_validator(mode='after')
    def validate_model_type_for_reduced_albumin(self):
        """Validate that model_type is compatible with ReducedAlbumin endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for ReducedAlbumin: {self.model_type.value}. Reduced albumin cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for ReducedAlbumin are: {', '.join(allowed_values)}."
            )
        return self

class WhiteBloodCells(Experiment):
    """Endpoint Information about white blood cells for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The total count of circulating white blood cells")
    measured_dose: str = Field(None, description="The dose at which white blood cell count is measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_white_blood_cells(self):
        """Validate that model_type is compatible with WhiteBloodCells endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for WhiteBloodCells: {self.model_type.value}. White blood cell count cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for WhiteBloodCells are: {', '.join(allowed_values)}."
            )
        return self

class ReducedLymphocytes(Experiment):
    """Endpoint Information about reduced lymphocytes for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The decrease in lymphocyte count, typically expressed as a count or percentage")
    measured_dose: str = Field(None, description="The dose at which reduced lymphocyte count is observed")
    
    @model_validator(mode='after')
    def validate_model_type_for_reduced_lymphocytes(self):
        """Validate that model_type is compatible with ReducedLymphocytes endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for ReducedLymphocytes: {self.model_type.value}. Reduced lymphocyte count cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for ReducedLymphocytes are: {', '.join(allowed_values)}."
            )
        return self

class ReducedNeutrophils(Experiment):
    """Endpoint Information about reduced neutrophils for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The decrease in neutrophil count, typically expressed as a count or percentage")
    measured_dose: str = Field(None, description="The dose at which reduced neutrophil count is observed")
    
    @model_validator(mode='after')
    def validate_model_type_for_reduced_neutrophils(self):
        """Validate that model_type is compatible with ReducedNeutrophils endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for ReducedNeutrophils: {self.model_type.value}. Reduced neutrophil count cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for ReducedNeutrophils are: {', '.join(allowed_values)}."
            )
        return self

class IncreasedAst(Experiment):
    """Endpoint Information about increased AST for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The elevated AST liver enzyme levels, typically expressed in U/L")
    measured_dose: str = Field(None, description="The dose at which increased AST is observed")
    
    @model_validator(mode='after')
    def validate_model_type_for_increased_ast(self):
        """Validate that model_type is compatible with IncreasedAst endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for IncreasedAst: {self.model_type.value}. Increased AST cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for IncreasedAst are: {', '.join(allowed_values)}."
            )
        return self

class IncreasedAlt(Experiment):
    """Endpoint Information about increased ALT for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The elevated ALT liver enzyme levels, typically expressed in U/L")
    measured_dose: str = Field(None, description="The dose at which increased ALT is observed")
    
    @model_validator(mode='after')
    def validate_model_type_for_increased_alt(self):
        """Validate that model_type is compatible with IncreasedAlt endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for IncreasedAlt: {self.model_type.value}. Increased ALT cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for IncreasedAlt are: {', '.join(allowed_values)}."
            )
        return self

# class PulmonaryToxicity(Experiment):
#     """Endpoint Information about lung inflammation, Interstitial Lung Disease, fibrosis or other forms of non-infectious lung injury for the given ADC tested on the experimental model with citations"""
#     citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
#     measured_value: str = Field(None, description="The inflammatory response in lung tissue, typically described qualitatively or quantitatively")
#     measured_dose: str = Field(None, description="The dose at which lung inflammation, Interstitial Lung Disease, fibrosis or other forms of non-infectious lung injury is observed")
    
#     @model_validator(mode='after')
#     def validate_model_type_for_pulmonary_toxicity(self):
#         """Validate that model_type is compatible with pulmonary toxicity endpoint."""
#         allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
#         if self.model_type not in allowed_model_types:
#             allowed_values = [mt.value for mt in allowed_model_types]
#             raise ValueError(
#                 f"For the model {self.model_name}, the measurement provided has an invalid combination for PulmonaryToxicity: {self.model_type.value}. PulmonaryToxicity cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for PulmonaryToxicity are: {', '.join(allowed_values)}."
#             )
#         return self

class PulmonaryToxicity(Experiment):
    """Endpoint Information about lung inflammation, Interstitial Lung Disease, fibrosis or other forms of non-infectious lung injury for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The lung inflammation and fibrosis, typically described qualitatively or quantitatively")
    measured_dose: str = Field(None, description="The dose at which interstitial lung disease is observed")
    
    @model_validator(mode='after')
    def validate_model_type_for_pulmonary_toxicity(self):
        """Validate that model_type is compatible with PulmonaryToxicity endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for PulmonaryToxicity: {self.model_type.value}. Interstitial lung disease cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for InterstitialLungDisease are: {', '.join(allowed_values)}."
            )
        return self

class TargetOrganIssues(Experiment):
    """Endpoint Information about target organ issues for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The organ-specific toxicities caused by ADCs, typically described qualitatively")
    measured_dose: str = Field(None, description="The dose at which target organ issues are observed")
    affected_organs: List[str] = Field(default_factory=list, description="List of specific organs affected by toxicity")
    
    @model_validator(mode='after')
    def validate_model_type_for_target_organ_issues(self):
        """Validate that model_type is compatible with TargetOrganIssues endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"For the model {self.model_name}, the measurement provided has an invalid combination for TargetOrganIssues: {self.model_type.value}. Target organ issues cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!\nThe valid model types for TargetOrganIssues are: {', '.join(allowed_values)}."
            )
        return self

# Function to get the appropriate endpoint model class based on the endpoint name
def get_endpoint_model(endpoint_name: EndpointName) -> Type[Endpoint]:
    """Get the appropriate endpoint model class based on the endpoint name"""
    # Convert enum name to class name format (remove underscores and capitalize each word)
    # For example: ANTIGEN_EXPRESSION -> AntigenExpression
    class_name = ''.join(word.capitalize() for word in endpoint_name.split('_'))
    
    # Get the module where this function is defined
    module = sys.modules[__name__]
    
    # Try to get the class by name from the module
    try:
        return getattr(module, class_name)
    except AttributeError:
        # If the class doesn't exist, return the base Endpoint class
        return Endpoint
